import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from PIL import Image, ImageTk, ImageGrab
import cv2
import numpy as np
import ctypes
import platform
import json
import os

# 全局快捷键支持
try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("Warning: pynput not available. Global hotkeys will only work when window has focus.")


from LogiUsbimplementation import ImplementationA
# Removed placeholder ImplementationA

class AutomationApp:
    # Removed DIAGNOSTIC PLACEHOLDER for add_action_to_script

    def __init__(self, root):
        self.root = root
        self.root.title("Automation UI")
        # Initial size
        window_width = 1200
        window_height = 500
        self.root.geometry(f"{window_width}x{window_height}")

        # Center the window
        self.root.update_idletasks() # Update tasks to get correct window info
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x_coordinate = (screen_width // 2) - (window_width // 2)
        y_coordinate = (screen_height // 2) - (window_height // 2)
        self.root.geometry(f"{window_width}x{window_height}+{x_coordinate}+{y_coordinate}")

        self.template_image = None
        self.automation_thread = None
        self.is_automating = False
        self.mouse_controller = None
        self.wyUsbHandle = None # To store the handle from wyhz_init()

        # Scripting variables
        self.script_steps = []
        self.is_workflow_running = False
        self.workflow_thread = None

        # Hotkey settings
        self.hotkey_start = "F10"
        self.hotkey_stop = "F12"
        self.hotkey_settings_file = "hotkey_settings.json"
        self.global_hotkey_listener = None  # 全局快捷键监听器

        # Styling
        style = ttk.Style()

        # 使用现代主题
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'winnative' in available_themes:
            style.theme_use('winnative')
        else:
            style.theme_use('clam')

        # 定义现代化outline按钮样式 - 白色背景，边框和文字颜色一致
        # Plain button style (white background, gray text and border)
        style.configure("Plain.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#6B7280',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Plain.TButton",
                 background=[('active', '#F9FAFB'),
                           ('pressed', '#F3F4F6'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#6B7280'),
                            ('!focus', '#6B7280'),
                            ('disabled', '#E5E7EB')])

        # Primary button style (white background, blue text and border)
        style.configure("Primary.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#3B82F6',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Primary.TButton",
                 background=[('active', '#EBF4FF'),
                           ('pressed', '#DBEAFE'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#3B82F6'),
                            ('!focus', '#3B82F6'),
                            ('disabled', '#E5E7EB')])

        # Success button style (white background, green text and border)
        style.configure("Success.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#10B981',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Success.TButton",
                 background=[('active', '#ECFDF5'),
                           ('pressed', '#D1FAE5'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#10B981'),
                            ('!focus', '#10B981'),
                            ('disabled', '#E5E7EB')])

        # Warning button style (white background, orange text and border)
        style.configure("Warning.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#F59E0B',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Warning.TButton",
                 background=[('active', '#FFFBEB'),
                           ('pressed', '#FEF3C7'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#F59E0B'),
                            ('!focus', '#F59E0B'),
                            ('disabled', '#E5E7EB')])

        # Danger button style (white background, red text and border)
        style.configure("Danger.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#EF4444',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Danger.TButton",
                 background=[('active', '#FEF2F2'),
                           ('pressed', '#FECACA'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#EF4444'),
                            ('!focus', '#EF4444'),
                            ('disabled', '#E5E7EB')])

        # Info button style (white background, gray text and border)
        style.configure("Info.TButton",
                       padding=(12, 8),
                       relief="solid",
                       background='white',
                       foreground='#6B7280',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Info.TButton",
                 background=[('active', '#F9FAFB'),
                           ('pressed', '#F3F4F6'),
                           ('disabled', '#F9FAFB')],
                 foreground=[('disabled', '#D1D5DB')],
                 bordercolor=[('focus', '#6B7280'),
                            ('!focus', '#6B7280'),
                            ('disabled', '#E5E7EB')])

        style.configure("TLabel", padding=5)
        style.configure("TEntry", padding=5)

        # --- Main Layout with Scrollbar ---
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True)

        self.main_canvas = tk.Canvas(main_frame)
        self.main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.main_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.main_canvas.configure(yscrollcommand=scrollbar.set)

        self.scrollable_frame = ttk.Frame(self.main_canvas)
        self.canvas_frame_id = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        self.scrollable_frame.bind("<Configure>", self._on_frame_configure)
        self.main_canvas.bind("<Configure>", self._on_canvas_configure)
        self.root.bind_all("<MouseWheel>", self._on_mouse_wheel) # Bind mouse wheel for scrolling

        # --- Content Frames (now inside scrollable_frame) ---

        # 创建标签面板容器
        self.notebook = ttk.Notebook(self.scrollable_frame)
        self.notebook.pack(padx=10, pady=10, fill="both", expand=True)

        # 标签1：基础自动化
        self.basic_automation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_automation_frame, text="基础自动化")

        # 标签2：脚本工作流
        self.script_workflow_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.script_workflow_frame, text="脚本工作流")

        # 标签3：脚本录制（预留）
        self.script_recording_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.script_recording_frame, text="脚本录制")

        # === 基础自动化标签内容 ===

        # Frame for VID/PID
        vid_pid_frame = ttk.LabelFrame(self.basic_automation_frame, text="Mouse Controller VID/PID", padding=10)
        vid_pid_frame.pack(padx=10, pady=10, fill="x")

        ttk.Label(vid_pid_frame, text="VID:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.vid_entry = ttk.Entry(vid_pid_frame, width=15)
        self.vid_entry.grid(row=0, column=1, padx=5, pady=5)
        self.vid_entry.insert(0, "046D") # Default VID example

        ttk.Label(vid_pid_frame, text="PID:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.pid_entry = ttk.Entry(vid_pid_frame, width=15)
        self.pid_entry.grid(row=1, column=1, padx=5, pady=5)
        self.pid_entry.insert(0, "C534") # Default PID example

        # Frame for Template
        template_frame = ttk.LabelFrame(self.basic_automation_frame, text="Template Image", padding=10)
        template_frame.pack(padx=10, pady=10, fill="x")

        # Create left and right frames for the two-column layout
        left_template_frame = ttk.Frame(template_frame)
        left_template_frame.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.BOTH, expand=True)

        right_template_frame = ttk.Frame(template_frame)
        right_template_frame.pack(side=tk.RIGHT, padx=5, pady=5, fill=tk.Y)

        # Left side: Image display
        self.template_display = ttk.Label(left_template_frame) # To show thumbnail
        self.template_display.pack(pady=5, anchor='center')

        # Right side: Buttons and label
        self.capture_template_button = ttk.Button(right_template_frame, text="📷 截取模板图像",
                                                style="Primary.TButton", command=self.capture_template)
        self.capture_template_button.pack(pady=5, fill='x')

        self.load_template_button = ttk.Button(right_template_frame, text="📁 加载模板文件",
                                             style="Info.TButton", command=self.load_template_from_file)
        self.load_template_button.pack(pady=5, fill='x')

        self.template_label = ttk.Label(right_template_frame, text="尚未捕获模板图像", wraplength=200) # wraplength for long filenames
        self.template_label.pack(pady=5)

        # Frame for Action Configuration
        action_config_frame = ttk.LabelFrame(self.basic_automation_frame, text="Action Configuration", padding=10)
        action_config_frame.pack(padx=10, pady=10, fill="x")

        # Found Location (read-only)
        ttk.Label(action_config_frame, text="Found X:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.found_x_entry = ttk.Entry(action_config_frame, width=10, state="readonly")
        self.found_x_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(action_config_frame, text="Found Y:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.found_y_entry = ttk.Entry(action_config_frame, width=10, state="readonly")
        self.found_y_entry.grid(row=0, column=3, padx=5, pady=5, sticky="w")

        ttk.Label(action_config_frame, text="Confidence:").grid(row=0, column=4, padx=5, pady=5, sticky="w")
        self.confidence_entry = ttk.Entry(action_config_frame, width=10, state="readonly")
        self.confidence_entry.grid(row=0, column=5, padx=5, pady=5, sticky="w")

        # Offset
        ttk.Label(action_config_frame, text="Offset X:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.offset_x_entry = ttk.Entry(action_config_frame, width=10)
        self.offset_x_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")
        self.offset_x_entry.insert(0, "0")

        ttk.Label(action_config_frame, text="Offset Y:").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        self.offset_y_entry = ttk.Entry(action_config_frame, width=10)
        self.offset_y_entry.grid(row=1, column=3, padx=5, pady=5, sticky="w")
        self.offset_y_entry.insert(0, "0")

        # Action Type
        ttk.Label(action_config_frame, text="Action Type:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.action_type_combo = ttk.Combobox(action_config_frame, values=["Left Click", "Right Click", "Double Click", "Press Key"], width=15)
        self.action_type_combo.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky="w")
        self.action_type_combo.current(0) # Default to "Left Click"
        self.action_type_combo.bind("<<ComboboxSelected>>", self.on_action_type_change)

        # Key to Press
        ttk.Label(action_config_frame, text="Key to Press:").grid(row=2, column=3, padx=5, pady=5, sticky="w") # Adjusted column
        self.key_press_entry = ttk.Entry(action_config_frame, width=15, state="disabled")
        self.key_press_entry.grid(row=2, column=4, columnspan=2, padx=5, pady=5, sticky="w") # Adjusted column & span

        # Step Interval (wait time after this step)
        ttk.Label(action_config_frame, text="步骤后等待 (秒):").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.step_interval_entry = ttk.Entry(action_config_frame, width=10)
        self.step_interval_entry.grid(row=3, column=1, padx=5, pady=5, sticky="w")
        self.step_interval_entry.insert(0, "1.0")

        # Frame for Automation Control
        automation_frame = ttk.LabelFrame(self.basic_automation_frame, text="Automation Control", padding=10)
        automation_frame.pack(padx=10, pady=10, fill="x")

        # Horizontal layout using pack
        interval_label = ttk.Label(automation_frame, text="Interval (seconds):")
        interval_label.pack(side=tk.LEFT, padx=(0, 5), pady=5)

        self.interval_entry = ttk.Entry(automation_frame, width=10)
        self.interval_entry.pack(side=tk.LEFT, pady=5)
        self.interval_entry.insert(0, "5")

        self.start_stop_button = ttk.Button(automation_frame, text="🚀 开始自动化",
                                           style="Success.TButton", command=self.toggle_automation)
        self.start_stop_button.pack(side=tk.RIGHT, padx=10, pady=5)

        # === 脚本工作流标签内容 ===

        # Frame for Script Workflow
        script_frame = ttk.LabelFrame(self.script_workflow_frame, text="Script Workflow", padding=10)
        script_frame.pack(padx=10, pady=10, fill="both", expand=True) # expand True

        # 创建按钮和控制区域的容器 - 使用横向布局支持自动换行
        script_controls_frame = ttk.Frame(script_frame)
        script_controls_frame.pack(fill="x", pady=5)

        # 创建按钮容器，使用横向布局
        buttons_container = ttk.Frame(script_controls_frame)
        buttons_container.pack(fill="x", pady=(0, 5))

        # 主要操作按钮 - 横向排列
        self.add_action_button = ttk.Button(buttons_container, text="➕ 添加动作",
                                            style="Primary.TButton", command=self.add_action_to_script)
        self.add_action_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.run_workflow_button = ttk.Button(buttons_container, text="▶️ 运行工作流",
                                              style="Success.TButton", command=self.run_workflow)
        self.run_workflow_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.stop_workflow_button = ttk.Button(buttons_container, text="⏹️ 停止工作流",
                                               style="Danger.TButton", command=self.stop_workflow, state="disabled")
        self.stop_workflow_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.clear_script_button = ttk.Button(buttons_container, text="🗑️ 清空脚本",
                                              style="Warning.TButton", command=self.clear_script)
        self.clear_script_button.pack(side=tk.LEFT, padx=3, pady=3)

        # 文件操作按钮 - 继续横向排列
        self.save_workflow_button = ttk.Button(buttons_container, text="💾 保存工作流",
                                               style="Info.TButton", command=self.save_workflow)
        self.save_workflow_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.load_workflow_button = ttk.Button(buttons_container, text="📂 加载工作流",
                                               style="Plain.TButton", command=self.load_workflow)
        self.load_workflow_button.pack(side=tk.LEFT, padx=3, pady=3)

        # 参数设置区域
        params_frame = ttk.Frame(script_controls_frame)
        params_frame.pack(fill="x", pady=(10, 3))

        # 执行次数设置
        ttk.Label(params_frame, text="执行次数 (0 为无限):").pack(side=tk.LEFT, padx=(0, 5))
        self.workflow_runs_entry = ttk.Entry(params_frame, width=8)
        self.workflow_runs_entry.pack(side=tk.LEFT, padx=(0, 15))
        self.workflow_runs_entry.insert(0, "1")

        # 全局步骤间隔设置
        ttk.Label(params_frame, text="全局步骤间隔 (秒):").pack(side=tk.LEFT, padx=(0, 5))
        self.workflow_interval_entry = ttk.Entry(params_frame, width=8)
        self.workflow_interval_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.workflow_interval_entry.insert(0, "0.5")

        # 脚本工作流
        self.script_text_area = tk.Text(script_frame, height=10, width=70, state="disabled") # Read-only for now
        self.script_text_area.pack(pady=5, padx=5, fill="both", expand=True)

        # 脚本编辑按钮
        script_edit_frame = ttk.Frame(script_frame)
        script_edit_frame.pack(fill="x", pady=5)

        self.edit_script_button = ttk.Button(script_edit_frame, text="✏️ 编辑选中步骤",
                                           style="Plain.TButton", command=self.edit_selected_script_step)
        self.edit_script_button.pack(side=tk.LEFT, padx=3, pady=3)

        # 绑定双击事件到脚本文本区域
        self.script_text_area.bind("<Double-Button-1>", self.on_script_double_click)



        # === 脚本录制标签内容 ===

        # 录制控制区域
        recording_control_frame = ttk.LabelFrame(self.script_recording_frame, text="录制控制", padding=10)
        recording_control_frame.pack(padx=10, pady=10, fill="x")

        # 录制状态和控制按钮
        recording_status_frame = ttk.Frame(recording_control_frame)
        recording_status_frame.pack(fill="x", pady=(0, 10))

        self.recording_status_label = ttk.Label(recording_status_frame, text="录制状态：未开始", font=('Segoe UI', 10, 'bold'))
        self.recording_status_label.pack(side=tk.LEFT)

        # 录制按钮容器
        recording_buttons_frame = ttk.Frame(recording_control_frame)
        recording_buttons_frame.pack(fill="x")

        self.start_recording_button = ttk.Button(recording_buttons_frame, text="🔴 开始录制",
                                               style="Danger.TButton", command=self.start_recording)
        self.start_recording_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.stop_recording_button = ttk.Button(recording_buttons_frame, text="⏹️ 停止录制",
                                              style="Warning.TButton", command=self.stop_recording, state="disabled")
        self.stop_recording_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.clear_recording_button = ttk.Button(recording_buttons_frame, text="🗑️ 清空录制",
                                               style="Plain.TButton", command=self.clear_recording)
        self.clear_recording_button.pack(side=tk.LEFT, padx=3, pady=3)

        self.apply_recording_button = ttk.Button(recording_buttons_frame, text="📋 应用到工作流",
                                               style="Success.TButton", command=self.apply_recording_to_workflow)
        self.apply_recording_button.pack(side=tk.LEFT, padx=3, pady=3)

        # 录制设置路子
        recording_settings_frame = ttk.LabelFrame(self.script_recording_frame, text="录制设置", padding=10)
        recording_settings_frame.pack(padx=10, pady=10, fill="x")

        # 录制选项
        options_frame = ttk.Frame(recording_settings_frame)
        options_frame.pack(fill="x")

        self.record_mouse_var = tk.BooleanVar(value=True)
        self.record_mouse_check = ttk.Checkbutton(options_frame, text="录制鼠标点击", variable=self.record_mouse_var)
        self.record_mouse_check.pack(side=tk.LEFT, padx=(0, 15))

        self.record_keyboard_var = tk.BooleanVar(value=False)
        self.record_keyboard_check = ttk.Checkbutton(options_frame, text="录制键盘输入", variable=self.record_keyboard_var)
        self.record_keyboard_check.pack(side=tk.LEFT, padx=(0, 15))

        self.auto_screenshot_var = tk.BooleanVar(value=True)
        self.auto_screenshot_check = ttk.Checkbutton(options_frame, text="自动截取模板", variable=self.auto_screenshot_var)
        self.auto_screenshot_check.pack(side=tk.LEFT)

        # 录制结果显示
        recording_result_frame = ttk.LabelFrame(self.script_recording_frame, text="录制结果", padding=10)
        recording_result_frame.pack(padx=10, pady=10, fill="both", expand=True)

        # 去掉, state="disabled" 允许用户编辑
        self.recording_text_area = tk.Text(recording_result_frame, height=10, width=70)
        self.recording_text_area.pack(pady=5, padx=5, fill="both", expand=True)

        # 录制结果操作按钮
        recording_result_buttons_frame = ttk.Frame(recording_result_frame)
        recording_result_buttons_frame.pack(fill="x", pady=5)

        # 添加编辑按钮
        self.edit_recording_button = ttk.Button(recording_result_buttons_frame, text="✏️ 编辑选中步骤",
                                              style="Plain.TButton", command=self.edit_selected_recording_step)
        self.edit_recording_button.pack(side=tk.LEFT, padx=3, pady=3)

        # 绑定双击事件到录制文本区域
        self.recording_text_area.bind("<Double-Button-1>", self.on_recording_double_click)


        # 录制相关变量初始化
        self.is_recording = False
        self.recorded_actions = []
        self.recording_listener = None
        self.mouse_listener = None
        self.keyboard_listener = None
        self.last_click_time = 0
        self.last_click_pos = (0, 0)
        self.double_click_threshold = 0.5  # 双击时间阈值（秒）

        # Status Bar
        self.status_bar = ttk.Label(root, text="Status: Idle", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=0, pady=0)

        # Autosave setup
        self.autosave_filepath = "autosave_workflow.json"
        self._autosave_load()
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Setup hotkeys and menu at the end of initialization
        self._initialize_hotkeys_and_menu()

    def _initialize_hotkeys_and_menu(self):
        """Initialize hotkeys and menu after all methods are defined"""
        self._load_hotkey_settings()
        self._setup_global_hotkeys()
        self._create_menu_bar()

    def _create_menu_bar(self):
        """Create the menu bar with settings menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="快捷键设置", command=self.open_hotkey_settings)

    def _load_hotkey_settings(self):
        """Load hotkey settings from file"""
        try:
            if os.path.exists(self.hotkey_settings_file):
                with open(self.hotkey_settings_file, 'r') as f:
                    settings = json.load(f)
                    self.hotkey_start = settings.get("start", "F10")
                    self.hotkey_stop = settings.get("stop", "F12")
        except Exception as e:
            print(f"Error loading hotkey settings: {e}")

    def _save_hotkey_settings(self):
        """Save hotkey settings to file"""
        try:
            settings = {
                "start": self.hotkey_start,
                "stop": self.hotkey_stop
            }
            with open(self.hotkey_settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Error saving hotkey settings: {e}")

    def open_hotkey_settings(self):
        """Open hotkey settings dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("快捷键设置")
        dialog.geometry("300x200")
        dialog.resizable(False, False)

        # Center the dialog
        dialog.transient(self.root)
        dialog.grab_set()

        # Start hotkey setting
        ttk.Label(dialog, text="启动工作流快捷键：").pack(pady=10)
        start_var = tk.StringVar(value=self.hotkey_start)
        start_entry = ttk.Entry(dialog, textvariable=start_var, width=20)
        start_entry.pack(pady=5)

        # Stop hotkey setting
        ttk.Label(dialog, text="停止工作流快捷键：").pack(pady=10)
        stop_var = tk.StringVar(value=self.hotkey_stop)
        stop_entry = ttk.Entry(dialog, textvariable=stop_var, width=20)
        stop_entry.pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        def save_settings():
            self.hotkey_start = start_var.get().strip()
            self.hotkey_stop = stop_var.get().strip()
            self._save_hotkey_settings()
            self._setup_global_hotkeys()
            messagebox.showinfo("设置", "快捷键设置已保存")
            dialog.destroy()

        def cancel_settings():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel_settings).pack(side=tk.LEFT, padx=5)

    def _setup_global_hotkeys(self):
        """Setup global hotkeys using pynput if available"""
        # 停止现有的监听器
        if self.global_hotkey_listener:
            try:
                self.global_hotkey_listener.stop()
            except:
                pass
            self.global_hotkey_listener = None

        # 清除窗口焦点快捷键
        try:
            self.root.unbind_all(f"<{self.hotkey_start}>")
            self.root.unbind_all(f"<{self.hotkey_stop}>")
        except:
            pass

        if PYNPUT_AVAILABLE:
            try:
                # 设置全局快捷键
                hotkey_combinations = {
                    self._convert_hotkey_to_pynput(self.hotkey_start): self._hotkey_start_workflow,
                    self._convert_hotkey_to_pynput(self.hotkey_stop): self._hotkey_stop_workflow
                }

                self.global_hotkey_listener = keyboard.GlobalHotKeys(hotkey_combinations)
                self.global_hotkey_listener.start()

                self.update_status(f"全局快捷键已设置：{self.hotkey_start}=启动，{self.hotkey_stop}=停止")
            except Exception as e:
                self.update_status(f"全局快捷键设置失败，使用窗口快捷键：{e}")
                self._setup_window_hotkeys()
        else:
            # 如果pynput不可用，使用窗口焦点快捷键
            self._setup_window_hotkeys()

    def _setup_window_hotkeys(self):
        """设置窗口焦点快捷键作为备选方案"""
        try:
            # Bind new hotkeys (only works when window has focus)
            self.root.bind_all(f"<{self.hotkey_start}>", lambda e: self._hotkey_start_workflow())
            self.root.bind_all(f"<{self.hotkey_stop}>", lambda e: self._hotkey_stop_workflow())

            self.update_status(f"窗口快捷键已设置：{self.hotkey_start}=启动，{self.hotkey_stop}=停止 (需要窗口焦点)")
        except Exception as e:
            self.update_status(f"快捷键设置失败：{e}")

    def _convert_hotkey_to_pynput(self, hotkey):
        """将快捷键字符串转换为pynput格式"""
        # 简单的转换，支持常见的功能键
        key_mapping = {
            'F1': '<f1>', 'F2': '<f2>', 'F3': '<f3>', 'F4': '<f4>',
            'F5': '<f5>', 'F6': '<f6>', 'F7': '<f7>', 'F8': '<f8>',
            'F9': '<f9>', 'F10': '<f10>', 'F11': '<f11>', 'F12': '<f12>',
            'Escape': '<esc>', 'Space': '<space>', 'Enter': '<enter>',
            'Tab': '<tab>', 'Shift': '<shift>', 'Ctrl': '<ctrl>', 'Alt': '<alt>'
        }

        return key_mapping.get(hotkey, hotkey.lower())

    def _hotkey_start_workflow(self):
        """Handle start workflow hotkey"""
        if not self.is_workflow_running and self.script_steps:
            self.run_workflow()

    def _hotkey_stop_workflow(self):
        """Handle stop workflow hotkey"""
        if self.is_workflow_running:
            self.stop_workflow()

    def _on_closing(self):
        # 停止全局快捷键监听器
        if self.global_hotkey_listener:
            try:
                self.global_hotkey_listener.stop()
            except:
                pass

        # 停止录制监听器
        if hasattr(self, 'is_recording') and self.is_recording:
            self._stop_recording_listeners()

        self._autosave_save()
        self.root.destroy()

    def _autosave_save(self):
        if not self.script_steps:
            # If script is empty, check if an old autosave file exists and remove it
            if os.path.exists(self.autosave_filepath):
                try:
                    asset_dir_name = os.path.splitext(self.autosave_filepath)[0] + "_assets"
                    if os.path.exists(asset_dir_name):
                        import shutil
                        shutil.rmtree(asset_dir_name)
                    os.remove(self.autosave_filepath)
                    print("Autosave file and assets removed as script is empty.")
                except Exception as e:
                    print(f"Error removing old autosave file: {e}")
            return

        # Re-use the save_workflow logic but with a fixed path
        filepath = self.autosave_filepath
        base_dir = os.path.dirname(os.path.abspath(filepath)) # Use absolute path for reliability
        asset_dir_name = os.path.splitext(os.path.basename(filepath))[0] + "_assets"
        asset_path = os.path.join(base_dir, asset_dir_name)

        try:
            if not os.path.exists(asset_path):
                os.makedirs(asset_path)
        except OSError as e:
            print(f"Autosave Error: Failed to create asset directory: {e}")
            return

        workflow_data = {
            "vid": self.vid_entry.get(),
            "pid": self.pid_entry.get(),
            "steps": []
        }

        try:
            for i, step in enumerate(self.script_steps):
                template_filename = f"step_{i+1}_template.png"
                template_filepath = os.path.join(asset_path, template_filename)
                if step["template_image"] is not None:
                    success = cv2.imwrite(template_filepath, step["template_image"])
                    if not success:
                        print(f"Autosave Error: Failed to save template image for step {i+1}")
                        continue
                else:
                    print(f"Autosave Warning: Step {i+1} has no template image to save")
                    continue
                step_data = {
                    "template_path": os.path.join(asset_dir_name, template_filename),
                    "offset_x": step["offset_x"],
                    "offset_y": step["offset_y"],
                    "action_type": step["action_type"],
                    "key_to_press": step["key_to_press"],
                    "template_name": step["template_name"],
                    "step_interval": step.get("step_interval", 1.0)  # Save individual step interval
                }
                workflow_data["steps"].append(step_data)

            with open(filepath, 'w') as f:
                json.dump(workflow_data, f, indent=4)
            print(f"Autosave: Workflow saved to {filepath}")

        except Exception as e:
            print(f"Autosave Error: {e}")

    def _autosave_load(self):
        filepath = self.autosave_filepath
        if not os.path.exists(filepath):
            self.update_status("No autosave file found. Starting fresh.")
            return

        # Re-use the load_workflow logic but with a fixed path
        base_dir = os.path.dirname(os.path.abspath(filepath))
        try:
            with open(filepath, 'r') as f:
                workflow_data = json.load(f)

            self.clear_script()
            self.vid_entry.delete(0, tk.END)
            self.vid_entry.insert(0, workflow_data.get("vid", "046D"))
            self.pid_entry.delete(0, tk.END)
            self.pid_entry.insert(0, workflow_data.get("pid", "C534"))

            for step_data in workflow_data.get("steps", []):
                template_path = os.path.join(base_dir, step_data["template_path"])
                if not os.path.exists(template_path):
                    continue
                template_image_cv = cv2.imread(template_path)
                if template_image_cv is None:
                    continue
                step_details = {
                    "template_image": template_image_cv,
                    "offset_x": step_data["offset_x"],
                    "offset_y": step_data["offset_y"],
                    "action_type": step_data["action_type"],
                    "key_to_press": step_data["key_to_press"],
                    "template_name": step_data["template_name"],
                    "step_interval": step_data.get("step_interval", 1.0)  # Load individual step interval
                }
                self.script_steps.append(step_details)
                self.script_text_area.config(state="normal")
                script_line = f"Step {len(self.script_steps)}: Find '{step_details['template_name']}', Offset({step_details['offset_x']},{step_details['offset_y']}), Action: {step_details['action_type']}"
                if step_details['action_type'] == "Press Key":
                    script_line += f" [{step_details['key_to_press']}]"
                script_line += f", Wait: {step_details['step_interval']}s"
                self.script_text_area.insert(tk.END, script_line + "\n")
                self.script_text_area.config(state="disabled")

            self.update_status(f"Autosaved workflow loaded from {filepath}")
        except Exception as e:
            self.update_status(f"Could not load autosave file: {e}")

    def on_action_type_change(self, event=None):
        selected_action = self.action_type_combo.get()
        if selected_action == "Press Key":
            self.key_press_entry.config(state="normal")
        else:
            self.key_press_entry.config(state="disabled")

    def update_status(self, message):
        self.status_bar.config(text=f"Status: {message}")
        self.root.update_idletasks()

    def capture_template(self):
        try:
            self.update_status("Capturing template...")
            # Simple delay to allow user to prepare the screen.
            # For a better UX, a small countdown window or a hotkey would be ideal.
            self.update_status("Preparing for region selection...")
            self.root.iconify() # Minimize main window

            # Using a short delay to ensure main window is minimized before overlay appears
            self.root.after(100, self.start_region_selection)

        except Exception as e:
            messagebox.showerror("Capture Error", f"Failed to initiate capture: {e}")
            self.update_status(f"Error initiating capture: {e}")
            if hasattr(self, 'selector_window') and self.selector_window:
                self.selector_window.destroy()
            self.root.deiconify()

    def start_region_selection(self):
        try:
            self.selector = RegionSelector(self.root, self._on_region_selected)
        except Exception as e:
            messagebox.showerror("Capture Error", f"Failed to start region selector: {e}")
            self.update_status(f"Error starting region selector: {e}")
            self.root.deiconify()

    def _on_region_selected(self, bbox):
        self.root.deiconify() # Restore main window
        if bbox:
            try:
                self.update_status(f"Region selected: {bbox}. Capturing...")
                #PIL's ImageGrab.grab() bbox is (x1, y1, x2, y2)
                # Ensure coordinates are ordered correctly (x1 < x2, y1 < y2)
                x1, y1, x2, y2 = bbox
                if x1 > x2: x1, x2 = x2, x1
                if y1 > y2: y1, y2 = y2, y1

                # Ensure width and height are positive
                if x1 == x2 or y1 == y2:
                    messagebox.showerror("Capture Error", "Invalid region selected (zero width or height).")
                    self.update_status("Error: Invalid region selected.")
                    return

                captured_region_pil = ImageGrab.grab(bbox=(x1, y1, x2, y2), all_screens=True)

                self.template_image = cv2.cvtColor(np.array(captured_region_pil), cv2.COLOR_RGB2BGR)
                self.template_label.config(text=f"Template captured ({self.template_image.shape[1]}x{self.template_image.shape[0]})")
                self.update_status("Template captured successfully from selected region.")

                # Display thumbnail
                thumb_pil = captured_region_pil.copy()
                thumb_pil.thumbnail((200, 150))
                self.template_tk_image = ImageTk.PhotoImage(thumb_pil)
                self.template_display.config(image=self.template_tk_image)

            except Exception as e:
                messagebox.showerror("Capture Error", f"Failed to capture selected region: {e}")
                self.update_status(f"Error capturing region: {e}")
        else:
            self.update_status("Template capture cancelled or failed.")

        if hasattr(self, 'selector') and self.selector:
            # Ensure selector resources are cleaned up if it didn't close itself
            if hasattr(self.selector, 'overlay') and self.selector.overlay.winfo_exists(): # Check if overlay exists before destroying
                self.selector.overlay.destroy()
            self.selector = None

    def load_template_from_file(self):
        try:
            filepath = filedialog.askopenfilename(
                title="Select Template Image",
                filetypes=(("PNG files", "*.png"), ("JPEG files", "*.jpg;*.jpeg"), ("All files", "*.*"))
            )
            if not filepath:
                self.update_status("Template loading cancelled.")
                return

            self.update_status(f"Loading template from {filepath}...")
            # Read with OpenCV, store in BGR format
            loaded_image_cv = cv2.imread(filepath)
            if loaded_image_cv is None:
                messagebox.showerror("Load Error", "Could not read image file. Ensure it's a valid image format supported by OpenCV.")
                self.update_status(f"Error: Could not read image file {filepath}")
                return

            self.template_image = loaded_image_cv
            self.template_label.config(text=f"Template loaded ({self.template_image.shape[1]}x{self.template_image.shape[0]})")
            self.update_status("Template loaded successfully from file.")

            # Display thumbnail using Pillow to open for PhotoImage compatibility
            pil_image = Image.open(filepath)
            pil_image.thumbnail((200, 150)) # Max width 200, max height 150
            self.template_tk_image = ImageTk.PhotoImage(pil_image)
            self.template_display.config(image=self.template_tk_image)

        except Exception as e:
            messagebox.showerror("Load Error", f"Failed to load template: {e}")
            self.update_status(f"Error loading template: {e}")

    def toggle_automation(self):
        if self.is_automating:
            self.stop_automation()
        else:
            self.start_automation()

    def start_automation(self):
        if self.template_image is None: # Check if template_image is None
            messagebox.showerror("Error", "Please capture or load a template image first.")
            return

        vid_str = self.vid_entry.get()
        pid_str = self.pid_entry.get()
        try:
            interval = float(self.interval_entry.get())
            if interval <= 0:
                raise ValueError("Interval must be positive.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid interval: {e}")
            return

        if not vid_str or not pid_str:
            messagebox.showerror("Input Error", "VID and PID cannot be empty.")
            return

        try:
            # Convert hex string VID/PID from UI to integers
            vid_int = int(vid_str, 16)
            pid_int = int(pid_str, 16)
        except ValueError:
            messagebox.showerror("Input Error", "VID and PID must be valid hexadecimal numbers (e.g., 046D).")
            return

        try:
            self.update_status(f"Initializing mouse controller (VID:{vid_str}, PID:{pid_str})...")
            if not self.mouse_controller: # Create instance if it doesn't exist
                self.mouse_controller = ImplementationA(vid=vid_int, pid=pid_int)

            self.update_status("ImplementationA object available. Initializing device...")
            # wyhz_init now stores the handle internally in mouse_controller
            # and also returns it. We might not need to store it separately in wyUsbHandle.
            # For now, we assume mouse_controller.wyhz_init() will raise error on failure
            # or mouse_controller.hkm_handle will be None.
            returned_handle = self.mouse_controller.wyhz_init()

            if not self.mouse_controller.hkm_handle or not returned_handle: # Check internal handle
                messagebox.showerror("Device Error", "Failed to initialize USB device or get handle (hkm_handle is None).")
                self.update_status("Error: Failed to initialize USB device via mouse_controller.")
                # self.mouse_controller.close_device_handle() # Ensure cleanup if partially initialized
                self.mouse_controller = None # Or just ensure hkm_handle is None
                return
            self.wyUsbHandle = returned_handle # Still store raw handle if needed elsewhere, though actions go via mouse_controller
            self.update_status("Mouse controller initialized and USB handle obtained.")
        except Exception as e:
            messagebox.showerror("Device Error", f"Failed to initialize mouse controller: {e}")
            self.update_status(f"Error initializing mouse controller: {e}")
            if self.mouse_controller:
                self.mouse_controller.close_device_handle() # Attempt to clean up
            self.mouse_controller = None
            self.wyUsbHandle = None # Ensure this is also cleared
            return

        self.is_automating = True
        self.start_stop_button.config(text="⏹️ 停止自动化", style="Danger.TButton")
        self.update_status("Automation started. UI will minimize.")

        # Minimize the main window
        # Adding a short delay before starting the thread to allow UI to update/minimize
        self.root.after(100, self._start_automation_thread, interval)

    def _start_automation_thread(self, interval):
        # self.root.iconify() # Minimize UI # Keep UI open for workflow
        self.automation_thread = threading.Thread(target=self.automation_loop, args=(interval,), daemon=True)
        self.automation_thread.start()

    def stop_automation(self):
        self.is_automating = False
        if self.automation_thread and self.automation_thread.is_alive():
             # Thread will exit due to self.is_automating flag
             # Optionally, one could join the thread if synchronous stop is critical,
             # but for UI responsiveness, letting it exit via flag is usually fine.
             # self.automation_thread.join(timeout=interval*1.5) # Example if join is needed
             pass

        # Restore the main window
        if self.root.state() == 'iconic':
            self.root.deiconify()

        self.start_stop_button.config(text="🚀 开始自动化", style="Success.TButton")
        self.update_status("Automation stopped. UI restored.")

        if self.mouse_controller and self.mouse_controller.hkm_handle: # Check internal handle
            try:
                # close_device_handle in ImplementationA now uses its internal handle
                self.mouse_controller.close_device_handle()
                self.update_status("USB device handle released by mouse_controller.")
            except Exception as e:
                self.update_status(f"Error releasing USB device handle via mouse_controller: {e}")

        # If workflow is not also trying to clean up, then nullify.
        # This logic might need refinement if both single auto and workflow manage same controller instance.
        if not self.is_workflow_running: # Only nullify if workflow isn't also using it
            self.mouse_controller = None
            self.wyUsbHandle = None # wyUsbHandle is just a copy of the raw handle, clear it too.

    def automation_loop(self, interval):
        while self.is_automating:
            if not self.mouse_controller or not self.mouse_controller.hkm_handle:
                self.update_status("Error: Mouse controller not available/initialized. Stopping automation.")
                self.is_automating = False
                break
            if self.template_image is None:
                self.update_status("Error: Template image is missing. Stopping automation.")
                self.is_automating = False # Stop loop if template disappears
                break
            try:
                self.update_status("Searching for template...")
                screenshot_pil = ImageGrab.grab()
                current_screen_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                if self.template_image is None:
                    self.update_status("Critical Error: Template became None during loop. Stopping.")
                    self.is_automating = False
                    break

                result = cv2.matchTemplate(current_screen_cv, self.template_image, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                threshold = 0.8
                if max_val >= threshold:
                    template_h, template_w = self.template_image.shape[:2]
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    # Update UI with found coordinates and confidence (thread-safe)
                    def _update_gui_found_info():
                        self.found_x_entry.config(state="normal")
                        self.found_y_entry.config(state="normal")
                        self.confidence_entry.config(state="normal")

                        self.found_x_entry.delete(0, tk.END)
                        self.found_x_entry.insert(0, str(max_loc[0]))
                        self.found_y_entry.delete(0, tk.END)
                        self.found_y_entry.insert(0, str(max_loc[1]))
                        self.confidence_entry.delete(0, tk.END)
                        self.confidence_entry.insert(0, f"{max_val:.2f}")

                        self.found_x_entry.config(state="readonly")
                        self.found_y_entry.config(state="readonly")
                        self.confidence_entry.config(state="readonly")

                    self.root.after(0, _update_gui_found_info)


                    self.update_status(f"Template found at ({max_loc[0]}, {max_loc[1]}) with conf {max_val:.2f}. Processing action.")

                    if self.mouse_controller and self.mouse_controller.hkm_handle:
                        # Get offset values
                        try:
                            offset_x = int(self.offset_x_entry.get())
                        except ValueError:
                            offset_x = 0
                        try:
                            offset_y = int(self.offset_y_entry.get())
                        except ValueError:
                            offset_y = 0

                        target_x = center_x + offset_x
                        target_y = center_y + offset_y

                        click_x_int = int(round(target_x))
                        click_y_int = int(round(target_y)) # Use target_y

                        action_type = self.action_type_combo.get()
                        key_to_press = self.key_press_entry.get()

                        self.mouse_controller.MoveTo(click_x_int, click_y_int)
                        time.sleep(0.05) # Small delay after move, before action

                        if action_type == "Left Click":
                            self.mouse_controller.LeftClick()
                            self.update_status(f"Left clicked at ({click_x_int}, {click_y_int}).")
                        elif action_type == "Right Click":
                            try:
                                self.mouse_controller.RightClick()
                                self.update_status(f"Right clicked at ({click_x_int}, {click_y_int}).")
                            except NotImplementedError as e:
                                self.update_status(f"Error: RightClick not implemented in controller: {e}")
                                print(f"DEBUG: RightClick failed: {e}")
                            except Exception as e:
                                self.update_status(f"Error during RightClick: {e}")
                                print(f"DEBUG: RightClick runtime error: {e}")
                        elif action_type == "Double Click":
                            self.mouse_controller.LeftClick()
                            time.sleep(0.08) # Delay between clicks for double click
                            self.mouse_controller.LeftClick()
                            self.update_status(f"Double clicked at ({click_x_int}, {click_y_int}).")
                        elif action_type == "Press Key":
                            if key_to_press:
                                try:
                                    self.mouse_controller.PressKey(key_to_press)
                                    self.update_status(f"Pressed key '{key_to_press}' at ({click_x_int}, {click_y_int}).")
                                except NotImplementedError as e:
                                    self.update_status(f"Error: PressKey not implemented in controller: {e}")
                                    print(f"DEBUG: PressKey failed: {e}")
                                except ValueError as e: # For unknown key string
                                    self.update_status(f"Error: Invalid key for PressKey: {e}")
                                    print(f"DEBUG: PressKey invalid key: {e}")
                                except Exception as e:
                                    self.update_status(f"Error during PressKey: {e}")
                                    print(f"DEBUG: PressKey runtime error: {e}")
                            else:
                                self.update_status("Warning: 'Press Key' selected but no key specified.")

                    else:
                        self.update_status("Error: Mouse controller not available for action.")
                        # print(f"DEBUG: wyUsbHandle not available. Would act at ({target_x}, {target_y}) with action {action_type}")
                        print(f"DEBUG: Mouse controller not available. Would act at ({target_x}, {target_y}) with action {action_type}")
                else:
                    self.update_status(f"Template not found (max conf: {max_val:.2f} < {threshold}).")

            except Exception as e:
                print(f"Error in automation loop: {e}")
                self.update_status(f"Error: {e}")

            for _ in range(int(interval * 10)):
                if not self.is_automating:
                    break
                time.sleep(0.1)

        self.update_status("Automation loop finished.")

    # --- Scripting Methods ---
    def add_action_to_script(self):
        if self.template_image is None:
            messagebox.showwarning("Scripting", "Please capture or load a template image first to add an action.")
            return

        # For simplicity, we'll store the template image itself (as np.array) in the script step.
        # For larger scripts or if saving/loading scripts to files is desired,
        # storing template file paths would be more appropriate.
        current_template_cv = self.template_image.copy() # Store a copy

        try:
            offset_x = int(self.offset_x_entry.get())
        except ValueError:
            offset_x = 0
        try:
            offset_y = int(self.offset_y_entry.get())
        except ValueError:
            offset_y = 0

        action_type = self.action_type_combo.get()
        key_to_press = self.key_press_entry.get() if action_type == "Press Key" else ""

        # Get step interval
        try:
            step_interval = float(self.step_interval_entry.get())
            if step_interval < 0:
                step_interval = 0.0
        except ValueError:
            step_interval = 1.0  # Default value

        step_details = {
            "template_image": current_template_cv,
            "offset_x": offset_x,
            "offset_y": offset_y,
            "action_type": action_type,
            "key_to_press": key_to_press,
            "template_name": self.template_label.cget("text"), # Get text from template_label
            "step_interval": step_interval  # Add individual step interval
        }
        self.script_steps.append(step_details)

        # Update script text area
        self.script_text_area.config(state="normal")
        script_line = f"Step {len(self.script_steps)}: Find '{step_details['template_name']}', Offset({offset_x},{offset_y}), Action: {action_type}"
        if action_type == "Press Key":
            script_line += f" [{key_to_press}]"
        script_line += f", Wait: {step_interval}s"
        self.script_text_area.insert(tk.END, script_line + "\n")
        self.script_text_area.config(state="disabled")
        self.update_status(f"Action added to script. Total steps: {len(self.script_steps)}")

    def run_workflow(self):
        if not self.script_steps:
            messagebox.showinfo("Workflow", "Script is empty. Add actions first.")
            return
        if self.is_automating:
            messagebox.showwarning("Workflow", "Cannot start workflow while single automation is running. Please stop it first.")
            return
        if self.is_workflow_running:
            messagebox.showwarning("Workflow", "Workflow is already running.")
            return

        try:
            runs = int(self.workflow_runs_entry.get())
            if runs < 0:
                raise ValueError("Execution count cannot be negative.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid execution count: {e}. Please enter a non-negative integer.")
            return

        try:
            step_interval = float(self.workflow_interval_entry.get())
            if step_interval < 0:
                raise ValueError("Step interval cannot be negative.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid step interval: {e}. Please enter a non-negative number.")
            return

        vid_str = self.vid_entry.get()
        pid_str = self.pid_entry.get()
        if not vid_str or not pid_str:
            messagebox.showerror("Input Error", "VID and PID cannot be empty for workflow execution.")
            return
        try:
            vid_int = int(vid_str, 16)
            pid_int = int(pid_str, 16)
        except ValueError:
            messagebox.showerror("Input Error", "VID and PID must be valid hexadecimal numbers for workflow.")
            return

        try:
            if not self.mouse_controller or not self.mouse_controller.hkm_handle:
                self.update_status(f"Workflow: Initializing mouse controller (VID:{vid_str}, PID:{pid_str})...")
                if not self.mouse_controller:
                    self.mouse_controller = ImplementationA(vid=vid_int, pid=pid_int)
                returned_handle = self.mouse_controller.wyhz_init()
                if not self.mouse_controller.hkm_handle or not returned_handle:
                    messagebox.showerror("Device Error", "Workflow: Failed to initialize USB device or get handle.")
                    self.update_status("Workflow Error: Failed to initialize USB device.")
                    if self.mouse_controller: self.mouse_controller.close_device_handle()
                    self.mouse_controller = None
                    self.wyUsbHandle = None
                    return
                self.wyUsbHandle = returned_handle
                self.update_status("Workflow: Mouse controller initialized.")
            else:
                self.update_status("Workflow: Using existing initialized mouse controller.")
        except Exception as e:
            messagebox.showerror("Device Error", f"Workflow: Failed to initialize mouse controller: {e}")
            self.update_status(f"Workflow Error: Init controller: {e}")
            if self.mouse_controller: self.mouse_controller.close_device_handle()
            self.mouse_controller = None
            self.wyUsbHandle = None
            return

        self.is_workflow_running = True
        self.run_workflow_button.config(state="disabled")
        self.add_action_button.config(state="disabled")
        self.clear_script_button.config(state="disabled")
        self.start_stop_button.config(state="disabled")
        self.stop_workflow_button.config(state="normal")
        self.update_status("Workflow started...")

        self.workflow_thread = threading.Thread(target=self.workflow_execution_manager, args=(runs, step_interval), daemon=True)
        self.workflow_thread.start()

    def workflow_execution_manager(self, total_runs, step_interval=0.5):
        run_count = 0
        is_infinite = (total_runs == 0)

        while self.is_workflow_running and (is_infinite or run_count < total_runs):
            if not self.is_workflow_running:
                break

            run_count += 1
            if is_infinite:
                self.update_status(f"Starting workflow execution cycle {run_count}...")
            else:
                self.update_status(f"Starting workflow execution {run_count}/{total_runs}...")

            # Execute one full workflow cycle
            self.workflow_loop(step_interval)

            # If the workflow was stopped during the loop, exit the manager loop
            if not self.is_workflow_running:
                break

            # Small delay between full workflow runs, can be made configurable
            if is_infinite or run_count < total_runs:
                self.update_status(f"Cycle {run_count} finished. Waiting before next run...")
                for _ in range(10): # e.g., 1s delay, checking stop flag
                    if not self.is_workflow_running: break
                    time.sleep(0.1)

        # Workflow finished or stopped
        self.is_workflow_running = False
        self.root.after(0, self._finalize_workflow_gui)

    def workflow_loop(self, default_step_interval=0.5):
        for i, step in enumerate(self.script_steps):
            if not self.is_workflow_running:
                self.update_status("Workflow stopped by user.")
                break

            # Get individual step interval or use default
            step_interval = step.get("step_interval", default_step_interval)

            self.update_status(f"Workflow: Executing Step {i+1}/{len(self.script_steps)}: Find '{step['template_name']}'...")

            current_template_cv = step["template_image"]
            if current_template_cv is None:
                self.update_status(f"Workflow Error: Step {i+1} has no template image. Skipping.")
                print(f"DEBUG: Workflow Step {i+1} missing template_image.")
                time.sleep(step_interval) # Pause before next step
                continue

            try:
                screenshot_pil = ImageGrab.grab()
                current_screen_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                result = cv2.matchTemplate(current_screen_cv, current_template_cv, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                threshold = 0.8 # Consider making this configurable per step in future
                if max_val >= threshold:
                    template_h, template_w = current_template_cv.shape[:2]
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    target_x = center_x + step["offset_x"]
                    target_y = center_y + step["offset_y"]
                    click_x_int = int(round(target_x))
                    click_y_int = int(round(target_y))

                    action_description = f"Action: {step['action_type']}"
                    if step['action_type'] == "Press Key":
                        action_description += f" [{step['key_to_press']}]"
                    self.update_status(f"Step {i+1}: Found at ({max_loc[0]},{max_loc[1]}), Conf: {max_val:.2f}. {action_description} at ({click_x_int},{click_y_int}).")

                    if self.mouse_controller and self.mouse_controller.hkm_handle:
                        self.mouse_controller.MoveTo(click_x_int, click_y_int)
                        time.sleep(0.05) # Small delay after move

                        action_type = step["action_type"]
                        key_to_press = step["key_to_press"]

                        try:
                            if action_type == "Left Click":
                                self.mouse_controller.LeftClick()
                            elif action_type == "Right Click":
                                self.mouse_controller.RightClick()
                            elif action_type == "Double Click":
                                self.mouse_controller.LeftClick()
                                time.sleep(0.08)
                                self.mouse_controller.LeftClick()
                            elif action_type == "Press Key":
                                if key_to_press:
                                    self.mouse_controller.PressKey(key_to_press)
                                else:
                                    self.update_status(f"Step {i+1} Warning: No key for Press Key action.")
                        except NotImplementedError as e:
                            self.update_status(f"Step {i+1} Error: Action '{action_type}' not implemented: {e}")
                            print(f"DEBUG: Workflow Step {i+1} NotImplementedError: {e}")
                        except ValueError as e: # For invalid key in PressKey
                             self.update_status(f"Step {i+1} Error: Invalid param for '{action_type}': {e}")
                             print(f"DEBUG: Workflow Step {i+1} ValueError: {e}")
                        except Exception as e:
                            self.update_status(f"Step {i+1} Error during action '{action_type}': {e}")
                            print(f"DEBUG: Workflow Step {i+1} Exception: {e}")

                        # Use individual step interval for delay after action
                        if step_interval > 0:
                            self.update_status(f"Step {i+1} completed. Waiting {step_interval}s before next step...")
                            delay_steps = max(1, int(step_interval * 10))  # Convert to 0.1s increments
                            for _ in range(delay_steps):
                                if not self.is_workflow_running: break
                                time.sleep(0.1)
                    else:
                        self.update_status(f"Step {i+1} Error: Mouse controller not available.")
                        print(f"DEBUG: Workflow Step {i+1} - Mouse controller missing.")
                        break # Critical error, stop workflow
                else:
                    self.update_status(f"Step {i+1}: Template not found (max conf: {max_val:.2f} < {threshold}). Skipping action.")
                    if step_interval > 0:
                        time.sleep(step_interval) # Pause if template not found

            except Exception as e:
                print(f"Error in workflow step {i+1}: {e}")
                self.update_status(f"Workflow Error Step {i+1}: {e}")
                if step_interval > 0:
                    time.sleep(step_interval) # Pause on error

        # This loop is now one cycle of the workflow.
        # The manager function will handle repetition and finalization.
        if not self.is_workflow_running:
             self.update_status("Workflow stopped during execution.")
        else:
             self.update_status("Workflow cycle finished.")

    def _finalize_workflow_gui(self):
        self.run_workflow_button.config(state="normal")
        self.add_action_button.config(state="normal")
        self.clear_script_button.config(state="normal")
        self.start_stop_button.config(state="normal")
        self.stop_workflow_button.config(state="disabled")

        # Decide if we close the handle here or keep it open if user might run again soon.
        # For now, let's close it to be safe.
        if self.mouse_controller and self.mouse_controller.hkm_handle: # Check internal handle
            try:
                # Check if single automation is also trying to use it.
                # If single automation is running, it will manage its own handle closure.
                if not self.is_automating:
                    self.mouse_controller.close_device_handle() # Uses internal handle
                    self.update_status("Workflow finished. USB device handle released by mouse_controller.")
                    self.mouse_controller = None # Nullify controller if we closed it
                    self.wyUsbHandle = None      # Nullify raw handle copy
                else:
                    # If single automation is running, it means it likely initialized the controller.
                    # Workflow should not close it in this case.
                    self.update_status("Workflow finished. Single automation is active, USB handle retained by it.")
            except Exception as e:
                self.update_status(f"Workflow: Error releasing USB handle via mouse_controller: {e}")
        else:
             self.update_status("Workflow finished.")

        # If mouse_controller became None above (because we closed it), self.wyUsbHandle should also be None.
        if not self.mouse_controller:
            self.wyUsbHandle = None


    def stop_workflow(self):
        if self.is_workflow_running:
            self.is_workflow_running = False # Signal the loop to stop
            # Thread will call _finalize_workflow_gui once it exits
            self.update_status("Stopping workflow...")
        else:
            self.update_status("Workflow is not running.")
            # Ensure GUI is reset if somehow stop is called when not running
            self._finalize_workflow_gui()


    def clear_script(self):
        if self.is_workflow_running:
            messagebox.showwarning("Scripting", "Cannot clear script while workflow is running.")
            return
        self.script_steps = []
        self.script_text_area.config(state="normal")
        self.script_text_area.delete(1.0, tk.END)
        self.script_text_area.config(state="disabled")
        self.update_status("Script cleared.")

    # --- Scrolling Methods ---
    def _on_frame_configure(self, event=None):
        """Reset the scroll region to encompass the inner frame"""
        self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))

    def _on_canvas_configure(self, event=None):
        """Reset the canvas window to encompass the inner frame"""
        canvas_width = event.width
        self.main_canvas.itemconfig(self.canvas_frame_id, width=canvas_width)

    def _on_mouse_wheel(self, event):
        """Process mouse wheel events for scrolling"""
        # The delta value is different on Windows vs. Linux/macOS
        if platform.system() == "Windows":
            self.main_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        else: # For Linux and macOS
            if event.num == 5: # Scroll down
                self.main_canvas.yview_scroll(1, "units")
            elif event.num == 4: # Scroll up
                self.main_canvas.yview_scroll(-1, "units")

    def save_workflow(self):
        if not self.script_steps:
            messagebox.showinfo("Save Workflow", "The script is empty. Nothing to save.")
            return

        filepath = filedialog.asksaveasfilename(
            title="Save Workflow File",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
        if not filepath:
            self.update_status("Save workflow cancelled.")
            return

        # Create a directory for assets next to the JSON file
        base_dir = os.path.dirname(filepath)
        file_name = os.path.basename(filepath)
        asset_dir_name = os.path.splitext(file_name)[0] + "_assets"
        asset_path = os.path.join(base_dir, asset_dir_name)

        try:
            if not os.path.exists(asset_path):
                os.makedirs(asset_path)
            self.update_status(f"Created asset directory: {asset_path}")
        except OSError as e:
            messagebox.showerror("Save Error", f"Failed to create asset directory: {e}")
            self.update_status(f"Error creating asset directory: {e}")
            return

        workflow_data = {
            "vid": self.vid_entry.get(),
            "pid": self.pid_entry.get(),
            "steps": []
        }

        try:
            for i, step in enumerate(self.script_steps):
                template_filename = f"step_{i+1}_template.png"
                template_filepath = os.path.join(asset_path, template_filename)

                # Save the numpy array (template_image) as a PNG file
                if step["template_image"] is not None:
                    success = cv2.imwrite(template_filepath, step["template_image"])
                    if not success:
                        raise Exception(f"Failed to save template image for step {i+1}")
                    self.update_status(f"Saved template image: {template_filepath}")
                else:
                    raise Exception(f"Step {i+1} has no template image to save")

                step_data = {
                    "template_path": os.path.join(asset_dir_name, template_filename), # Relative path
                    "offset_x": step["offset_x"],
                    "offset_y": step["offset_y"],
                    "action_type": step["action_type"],
                    "key_to_press": step["key_to_press"],
                    "template_name": step["template_name"],
                    "step_interval": step.get("step_interval", 1.0)  # Save individual step interval
                }
                workflow_data["steps"].append(step_data)

            with open(filepath, 'w') as f:
                json.dump(workflow_data, f, indent=4)

            self.update_status(f"Workflow saved successfully to {filepath}")
            messagebox.showinfo("Save Workflow", "Workflow and all template images have been saved.")

        except Exception as e:
            messagebox.showerror("Save Error", f"An error occurred while saving the workflow: {e}")
            self.update_status(f"Error saving workflow: {e}")

    def load_workflow(self):
        if self.is_workflow_running:
            messagebox.showwarning("Load Workflow", "Cannot load a new workflow while one is running.")
            return

        filepath = filedialog.askopenfilename(
            title="Load Workflow File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
        if not filepath:
            self.update_status("Load workflow cancelled.")
            return

        base_dir = os.path.dirname(filepath)

        try:
            with open(filepath, 'r') as f:
                workflow_data = json.load(f)

            # Clear existing script first
            self.clear_script()

            # Load VID/PID
            self.vid_entry.delete(0, tk.END)
            self.vid_entry.insert(0, workflow_data.get("vid", "046D"))
            self.pid_entry.delete(0, tk.END)
            self.pid_entry.insert(0, workflow_data.get("pid", "C534"))

            for step_data in workflow_data.get("steps", []):
                template_path = os.path.join(base_dir, step_data["template_path"])
                self.update_status(f"Loading template: {template_path}")

                if not os.path.exists(template_path):
                    error_msg = f"Template image not found: {template_path}. Skipping this step."
                    messagebox.showwarning("Load Warning", error_msg)
                    self.update_status(f"Warning: {error_msg}")
                    continue

                # Load template image with OpenCV
                template_image_cv = cv2.imread(template_path)
                if template_image_cv is None:
                    error_msg = f"Failed to load template image: {template_path}. Skipping this step."
                    messagebox.showwarning("Load Warning", error_msg)
                    self.update_status(f"Warning: {error_msg}")
                    continue

                self.update_status(f"Successfully loaded template: {template_path}")

                step_details = {
                    "template_image": template_image_cv,
                    "offset_x": step_data["offset_x"],
                    "offset_y": step_data["offset_y"],
                    "action_type": step_data["action_type"],
                    "key_to_press": step_data["key_to_press"],
                    "template_name": step_data["template_name"],
                    "step_interval": step_data.get("step_interval", 1.0)  # Load individual step interval
                }
                self.script_steps.append(step_details)

                # Update the UI text area
                self.script_text_area.config(state="normal")
                script_line = f"Step {len(self.script_steps)}: Find '{step_details['template_name']}', Offset({step_details['offset_x']},{step_details['offset_y']}), Action: {step_details['action_type']}"
                if step_details['action_type'] == "Press Key":
                    script_line += f" [{step_details['key_to_press']}]"
                script_line += f", Wait: {step_details['step_interval']}s"
                self.script_text_area.insert(tk.END, script_line + "\n")
                self.script_text_area.config(state="disabled")

            self.update_status(f"Workflow loaded successfully from {filepath}")
            messagebox.showinfo("Load Workflow", "Workflow loaded successfully.")

        except json.JSONDecodeError:
            messagebox.showerror("Load Error", "Invalid JSON file. The workflow file may be corrupted.")
            self.update_status("Error: Invalid JSON file.")
        except KeyError as e:
            messagebox.showerror("Load Error", f"Missing expected data in workflow file: {e}. The file might be from an incompatible version.")
            self.update_status(f"Error: Missing data in workflow file: {e}")
        except Exception as e:
            messagebox.showerror("Load Error", f"An error occurred while loading the workflow: {e}")
            self.update_status(f"Error loading workflow: {e}")

    # --- 脚本录制功能方法 ---
    def start_recording(self):
        """开始录制用户操作"""
        if self.is_recording:
            messagebox.showwarning("录制", "录制已在进行中")
            return

        if not PYNPUT_AVAILABLE:
            messagebox.showerror("录制错误", "录制功能需要pynput库支持，请安装pynput")
            return

        self.is_recording = True
        self.recorded_actions = []
        self.recording_start_time = time.time()

        # 更新UI状态
        self.recording_status_label.config(text="录制状态：录制中...", foreground="red")
        self.start_recording_button.config(state="disabled")
        self.stop_recording_button.config(state="normal")

        # 清空录制结果显示
        self.recording_text_area.config(state="normal")
        self.recording_text_area.delete(1.0, tk.END)
        self.recording_text_area.config(state="disabled")

        # 启动事件监听器
        self._start_recording_listeners()

        self.update_status("开始录制用户操作...")

    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            return

        self.is_recording = False

        # 停止事件监听器
        self._stop_recording_listeners()

        # 更新UI状态
        self.recording_status_label.config(text=f"录制状态：已完成 ({len(self.recorded_actions)} 个动作)", foreground="green")
        self.start_recording_button.config(state="normal")
        self.stop_recording_button.config(state="disabled")

        # 显示录制结果
        self._display_recorded_actions()

        self.update_status(f"录制完成，共记录 {len(self.recorded_actions)} 个动作")

    def clear_recording(self):
        """清空录制结果"""
        if self.is_recording:
            messagebox.showwarning("录制", "请先停止录制")
            return

        self.recorded_actions = []
        self.recording_text_area.config(state="normal")
        self.recording_text_area.delete(1.0, tk.END)
        self.recording_text_area.config(state="disabled")

        self.recording_status_label.config(text="录制状态：未开始", foreground="black")
        self.update_status("录制结果已清空")

    def apply_recording_to_workflow(self):
        """将录制结果应用到工作流"""
        if not self.recorded_actions:
            messagebox.showinfo("应用录制", "没有录制的动作可以应用")
            return

        # 切换到脚本工作流标签
        self.notebook.select(self.script_workflow_frame)

        applied_count = 0
        for action in self.recorded_actions:
            if action['type'] == 'click' and action.get('template_image') is not None:
                # 设置模板图像
                self.template_image = action['template_image']

                # 设置偏移
                self.offset_x_entry.delete(0, tk.END)
                self.offset_x_entry.insert(0, str(action.get('offset_x', 0)))
                self.offset_y_entry.delete(0, tk.END)
                self.offset_y_entry.insert(0, str(action.get('offset_y', 0)))

                # 设置动作类型
                if action.get('button') == 'right':
                    self.action_type_combo.set("Right Click")
                elif action.get('double'):
                    self.action_type_combo.set("Double Click")
                else:
                    self.action_type_combo.set("Left Click")

                # 设置步骤间隔
                self.step_interval_entry.delete(0, tk.END)
                self.step_interval_entry.insert(0, str(action.get('interval', 1.0)))

                # 更新模板标签
                self.template_label.config(text=f"录制模板 {applied_count + 1}")

                # 添加到脚本
                self.add_action_to_script()
                applied_count += 1

            elif action['type'] == 'key':
                # 设置按键动作
                self.action_type_combo.set("Press Key")
                self.key_press_entry.config(state="normal")
                self.key_press_entry.delete(0, tk.END)
                self.key_press_entry.insert(0, action.get('key', ''))

                # 设置步骤间隔
                self.step_interval_entry.delete(0, tk.END)
                self.step_interval_entry.insert(0, str(action.get('interval', 1.0)))

                # 如果有模板图像，使用它；否则使用当前模板
                if action.get('template_image') is not None:
                    self.template_image = action['template_image']
                    self.template_label.config(text=f"录制模板 {applied_count + 1}")

                # 添加到脚本
                if self.template_image is not None:
                    self.add_action_to_script()
                    applied_count += 1

        messagebox.showinfo("应用录制", f"成功应用 {applied_count} 个动作到工作流")
        self.update_status(f"已将 {applied_count} 个录制动作应用到工作流")

    def _start_recording_listeners(self):
        """启动录制事件监听器"""
        try:
            from pynput import mouse, keyboard

            # 鼠标监听器
            if self.record_mouse_var.get():
                self.mouse_listener = mouse.Listener(
                    on_click=self._on_mouse_click
                )
                self.mouse_listener.start()

            # 键盘监听器
            if self.record_keyboard_var.get():
                self.keyboard_listener = keyboard.Listener(
                    on_press=self._on_key_press
                )
                self.keyboard_listener.start()

        except Exception as e:
            self.update_status(f"启动录制监听器失败: {e}")
            messagebox.showerror("录制错误", f"启动录制监听器失败: {e}")

    def _stop_recording_listeners(self):
        """停止录制事件监听器"""
        try:
            if hasattr(self, 'mouse_listener') and self.mouse_listener:
                self.mouse_listener.stop()
                self.mouse_listener = None

            if hasattr(self, 'keyboard_listener') and self.keyboard_listener:
                self.keyboard_listener.stop()
                self.keyboard_listener = None

        except Exception as e:
            self.update_status(f"停止录制监听器失败: {e}")

    def _on_mouse_click(self, x, y, button, pressed):
        """处理鼠标点击事件"""
        if not self.is_recording or not pressed:
            return

        try:
            from pynput import mouse

            # 计算时间间隔
            current_time = time.time()
            interval = current_time - getattr(self, 'last_action_time', self.recording_start_time)

            # 检测双击
            is_double_click = False
            if (current_time - self.last_click_time < self.double_click_threshold and
                abs(x - self.last_click_pos[0]) < 5 and abs(y - self.last_click_pos[1]) < 5 and
                button == mouse.Button.left):
                is_double_click = True
                # 如果是双击，移除上一个单击记录
                if self.recorded_actions and self.recorded_actions[-1]['type'] == 'click':
                    self.recorded_actions.pop()

            self.last_click_time = current_time
            self.last_click_pos = (x, y)
            self.last_action_time = current_time

            # 创建动作记录
            action = {
                'type': 'click',
                'x': x,
                'y': y,
                'button': 'left' if button == mouse.Button.left else 'right',
                'double': is_double_click,
                'timestamp': current_time,
                'interval': round(interval, 2) if interval > 0.1 else 0.5,
                'template_image': None,
                'offset_x': 0,
                'offset_y': 0
            }

            # 如果启用自动截图，截取点击位置的模板
            if self.auto_screenshot_var.get():
                template_image = self._capture_click_template(x, y)
                if template_image is not None:
                    action['template_image'] = template_image

            self.recorded_actions.append(action)

            # 实时更新显示
            self._update_recording_display(action)

        except Exception as e:
            self.update_status(f"录制鼠标点击失败: {e}")

    def _on_key_press(self, key):
        """处理键盘按键事件"""
        if not self.is_recording:
            return

        try:
            # 计算时间间隔
            current_time = time.time()
            interval = current_time - getattr(self, 'last_action_time', self.recording_start_time)
            self.last_action_time = current_time

            # 获取按键名称
            key_name = self._get_key_name(key)
            if not key_name:
                return  # 忽略无法识别的按键

            # 创建动作记录
            action = {
                'type': 'key',
                'key': key_name,
                'timestamp': current_time,
                'interval': round(interval, 2) if interval > 0.1 else 0.5,
                'template_image': None
            }

            # 如果启用自动截图，截取当前屏幕中心区域作为模板
            if self.auto_screenshot_var.get():
                template_image = self._capture_center_template()
                if template_image is not None:
                    action['template_image'] = template_image

            self.recorded_actions.append(action)

            # 实时更新显示
            self._update_recording_display(action)

        except Exception as e:
            self.update_status(f"录制键盘按键失败: {e}")

    def _get_key_name(self, key):
        """获取按键名称"""
        try:
            from pynput import keyboard

            # 特殊按键映射
            special_keys = {
                keyboard.Key.space: 'space',
                keyboard.Key.enter: 'enter',
                keyboard.Key.tab: 'tab',
                keyboard.Key.esc: 'escape',
                keyboard.Key.backspace: 'backspace',
                keyboard.Key.delete: 'delete',
                keyboard.Key.shift: 'shift',
                keyboard.Key.ctrl: 'ctrl',
                keyboard.Key.alt: 'alt',
                keyboard.Key.up: 'up',
                keyboard.Key.down: 'down',
                keyboard.Key.left: 'left',
                keyboard.Key.right: 'right',
            }

            if key in special_keys:
                return special_keys[key]
            elif hasattr(key, 'char') and key.char:
                return key.char
            else:
                return str(key).replace('Key.', '') if hasattr(key, 'name') else None

        except Exception:
            return None

    def _capture_click_template(self, x, y, size=50):
        """截取点击位置周围的模板图像"""
        try:
            # 计算截取区域
            left = max(0, x - size // 2)
            top = max(0, y - size // 2)
            right = left + size
            bottom = top + size

            # 截取屏幕
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
            template_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            return template_image

        except Exception as e:
            self.update_status(f"截取点击模板失败: {e}")
            return None

    def _capture_center_template(self, size=100):
        """截取屏幕中心区域作为模板"""
        try:
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 计算中心区域
            center_x = screen_width // 2
            center_y = screen_height // 2
            left = center_x - size // 2
            top = center_y - size // 2
            right = left + size
            bottom = top + size

            # 截取屏幕
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
            template_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            return template_image

        except Exception as e:
            self.update_status(f"截取中心模板失败: {e}")
            return None

    def _update_recording_display(self, action):
        """实时更新录制显示"""
        try:
            self.recording_text_area.config(state="normal")

            if action['type'] == 'click':
                click_type = "双击" if action.get('double', False) else f"{action['button']}键点击"
                action_text = f"步骤 {len(self.recorded_actions)}: 鼠标{click_type} ({action['x']}, {action['y']})"
                if action['template_image'] is not None:
                    action_text += " [已截取模板]"
                action_text += f" - 间隔: {action['interval']}s\n"
            elif action['type'] == 'key':
                action_text = f"步骤 {len(self.recorded_actions)}: 按键 '{action['key']}'"
                if action['template_image'] is not None:
                    action_text += " [已截取模板]"
                action_text += f" - 间隔: {action['interval']}s\n"
            else:
                action_text = f"步骤 {len(self.recorded_actions)}: 未知动作\n"

            self.recording_text_area.insert(tk.END, action_text)
            self.recording_text_area.see(tk.END)  # 滚动到底部
            self.recording_text_area.config(state="disabled")

            # 更新状态标签
            self.recording_status_label.config(text=f"录制状态：录制中... ({len(self.recorded_actions)} 个动作)")

        except Exception as e:
            self.update_status(f"更新录制显示失败: {e}")

    def on_recording_double_click(self, event):
        """处理录制文本区域的双击事件"""
        try:
            # 获取点击位置的行号
            index = self.recording_text_area.index(tk.INSERT)
            line_num = int(index.split('.')[0])

            # 获取该行的内容
            line_start = f"{line_num}.0"
            line_end = f"{line_num}.end"
            line_content = self.recording_text_area.get(line_start, line_end)

            # 解析步骤编号
            step_num = self._parse_recording_step_number(line_content)
            if step_num is not None:
                self._edit_recording_step(step_num)
        except Exception as e:
            self.update_status(f"双击编辑失败: {e}")

    def edit_selected_recording_step(self):
        """编辑选中的录制步骤"""
        try:
            # 获取当前光标位置
            current_index = self.recording_text_area.index(tk.INSERT)
            line_num = int(current_index.split('.')[0])

            # 获取该行的内容
            line_start = f"{line_num}.0"
            line_end = f"{line_num}.end"
            line_content = self.recording_text_area.get(line_start, line_end)

            # 解析步骤编号
            step_num = self._parse_recording_step_number(line_content)
            if step_num is not None:
                self._edit_recording_step(step_num)
            else:
                messagebox.showinfo("编辑步骤", "请将光标放在要编辑的步骤行上")
        except Exception as e:
            self.update_status(f"编辑录制步骤失败: {e}")

    def _parse_recording_step_number(self, line_content):
        """从录制行内容中解析步骤编号"""
        import re
        # 匹配 "步骤 X:" 格式
        match = re.match(r'步骤\s*(\d+):', line_content.strip())
        if match:
            return int(match.group(1)) - 1  # 转换为0基索引
        return None

    def _edit_recording_step(self, step_index):
        """编辑指定的录制步骤"""
        if step_index < 0 or step_index >= len(self.recorded_actions):
            messagebox.showerror("编辑错误", "无效的步骤索引")
            return

        action = self.recorded_actions[step_index]
        current_interval = action.get('interval', 1.0)

        # 生成步骤信息
        if action['type'] == 'click':
            click_type = "双击" if action.get('double', False) else f"{action['button']}键点击"
            info_text = f"录制步骤 {step_index + 1}\n鼠标{click_type} ({action['x']}, {action['y']})"
        elif action['type'] == 'key':
            info_text = f"录制步骤 {step_index + 1}\n按键 '{action['key']}'"
        else:
            info_text = f"录制步骤 {step_index + 1}\n未知动作"

        def save_callback(new_interval):
            # 更新数据
            self.recorded_actions[step_index]['interval'] = new_interval
            # 刷新显示
            self._display_recorded_actions()
            self.update_status(f"已更新录制步骤 {step_index + 1} 的间隔时间为 {new_interval}s")

        # 使用通用对话框
        self._create_time_edit_dialog(
            f"编辑录制步骤 {step_index + 1}",
            current_interval,
            info_text,
            save_callback
        )

    def on_script_double_click(self, event):
        """处理脚本文本区域的双击事件"""
        try:
            # 获取点击位置的行号
            index = self.script_text_area.index(tk.INSERT)
            line_num = int(index.split('.')[0])

            # 获取该行的内容
            line_start = f"{line_num}.0"
            line_end = f"{line_num}.end"
            line_content = self.script_text_area.get(line_start, line_end)

            # 解析步骤编号
            step_num = self._parse_script_step_number(line_content)
            if step_num is not None:
                self._edit_script_step(step_num)
        except Exception as e:
            self.update_status(f"双击编辑脚本失败: {e}")

    def edit_selected_script_step(self):
        """编辑选中的脚本步骤"""
        try:
            # 获取当前光标位置
            current_index = self.script_text_area.index(tk.INSERT)
            line_num = int(current_index.split('.')[0])

            # 获取该行的内容
            line_start = f"{line_num}.0"
            line_end = f"{line_num}.end"
            line_content = self.script_text_area.get(line_start, line_end)

            # 解析步骤编号
            step_num = self._parse_script_step_number(line_content)
            if step_num is not None:
                self._edit_script_step(step_num)
            else:
                messagebox.showinfo("编辑步骤", "请将光标放在要编辑的步骤行上")
        except Exception as e:
            self.update_status(f"编辑脚本步骤失败: {e}")

    def _parse_script_step_number(self, line_content):
        """从脚本行内容中解析步骤编号"""
        import re
        # 匹配 "Step X:" 格式
        match = re.match(r'Step\s*(\d+):', line_content.strip())
        if match:
            return int(match.group(1)) - 1  # 转换为0基索引
        return None

    def _edit_script_step(self, step_index):
        """编辑指定的脚本步骤"""
        if step_index < 0 or step_index >= len(self.script_steps):
            messagebox.showerror("编辑错误", "无效的步骤索引")
            return

        step = self.script_steps[step_index]
        current_interval = step.get('step_interval', 1.0)
        current_offset_x = step.get('offset_x', 0)
        current_offset_y = step.get('offset_y', 0)

        # 生成步骤信息
        info_text = f"脚本步骤 {step_index + 1}\n"
        info_text += f"模板: {step['template_name']}\n"
        info_text += f"当前偏移: ({current_offset_x}, {current_offset_y})\n"
        info_text += f"动作: {step['action_type']}"
        if step['action_type'] == "Press Key":
            info_text += f" [{step['key_to_press']}]"

        def save_callback(new_interval, new_offset_x, new_offset_y):
            # 更新数据
            self.script_steps[step_index]['step_interval'] = new_interval
            self.script_steps[step_index]['offset_x'] = new_offset_x
            self.script_steps[step_index]['offset_y'] = new_offset_y
            # 刷新显示
            self._refresh_script_display()
            self.update_status(f"已更新脚本步骤 {step_index + 1}: 等待时间={new_interval}s, 偏移=({new_offset_x},{new_offset_y})")

        # 使用新的综合编辑对话框
        self._create_step_edit_dialog(
            f"编辑脚本步骤 {step_index + 1}",
            current_interval,
            current_offset_x,
            current_offset_y,
            info_text,
            save_callback
        )

    def _refresh_script_display(self):
        """刷新脚本工作流显示"""
        self.script_text_area.config(state="normal")
        self.script_text_area.delete(1.0, tk.END)

        for i, step in enumerate(self.script_steps):
            script_line = f"Step {i+1}: Find '{step['template_name']}', Offset({step['offset_x']},{step['offset_y']}), Action: {step['action_type']}"
            if step['action_type'] == "Press Key":
                script_line += f" [{step['key_to_press']}]"
            script_line += f", Wait: {step['step_interval']}s"
            self.script_text_area.insert(tk.END, script_line + "\n")

        self.script_text_area.config(state="disabled")

    def _validate_time_input(self, value_str, field_name="时间"):
        """验证时间输入的有效性"""
        try:
            value = float(value_str.strip())
            if value < 0:
                messagebox.showerror("输入错误", f"{field_name}不能为负数")
                return None
            if value > 3600:  # 限制最大1小时
                result = messagebox.askyesno("确认输入", f"{field_name} {value}秒 (约{value/60:.1f}分钟) 较长，确定要设置吗？")
                if not result:
                    return None
            return value
        except ValueError:
            messagebox.showerror("输入错误", f"请输入有效的{field_name}数值")
            return None

    def _validate_offset_input(self, value_str, field_name="偏移"):
        """验证偏移输入"""
        try:
            value = int(value_str.strip())
            # 偏移可以为负数，但设置合理范围
            if value < -1000 or value > 1000:
                messagebox.showerror("输入错误", f"{field_name}值应在-1000到1000之间")
                return None
            return value
        except ValueError:
            messagebox.showerror("输入错误", f"请输入有效的{field_name}整数值")
            return None

    def _create_time_edit_dialog(self, title, current_value, info_text, save_callback):
        """创建通用的时间编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("400x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 信息显示
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(info_frame, text=info_text, font=('Segoe UI', 9), justify=tk.LEFT).pack()

        # 时间编辑
        time_frame = ttk.Frame(dialog)
        time_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(time_frame, text="时间 (秒):").pack(side=tk.LEFT)
        time_var = tk.StringVar(value=str(current_value))
        time_entry = ttk.Entry(time_frame, textvariable=time_var, width=10)
        time_entry.pack(side=tk.LEFT, padx=(5, 0))
        time_entry.select_range(0, tk.END)
        time_entry.focus()

        # 快捷设置按钮
        quick_frame = ttk.Frame(dialog)
        quick_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(quick_frame, text="快捷设置:").pack(side=tk.LEFT)

        quick_values = [0.1, 0.5, 1.0, 2.0, 5.0]
        for val in quick_values:
            btn = ttk.Button(quick_frame, text=f"{val}s", width=6,
                           command=lambda v=val: time_var.set(str(v)))
            btn.pack(side=tk.LEFT, padx=2)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=5)

        def save_changes():
            new_value = self._validate_time_input(time_var.get())
            if new_value is not None:
                save_callback(new_value)
                dialog.destroy()

        def cancel_changes():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel_changes).pack(side=tk.RIGHT)

        # 绑定快捷键
        dialog.bind('<Return>', lambda e: save_changes())
        dialog.bind('<Escape>', lambda e: cancel_changes())

        return dialog

    def _create_step_edit_dialog(self, title, current_interval, current_offset_x, current_offset_y, info_text, save_callback):
        """创建综合的步骤编辑对话框，支持编辑时间间隔和偏移信息"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("450x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 信息显示
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(info_frame, text=info_text, font=('Segoe UI', 9), justify=tk.LEFT).pack()

        # 分隔线
        ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=5)

        # 时间间隔编辑
        time_frame = ttk.Frame(dialog)
        time_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(time_frame, text="等待时间 (秒):").pack(side=tk.LEFT)
        time_var = tk.StringVar(value=str(current_interval))
        time_entry = ttk.Entry(time_frame, textvariable=time_var, width=10)
        time_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 时间快捷设置按钮
        time_quick_frame = ttk.Frame(dialog)
        time_quick_frame.pack(fill="x", padx=10, pady=2)
        ttk.Label(time_quick_frame, text="快捷设置:").pack(side=tk.LEFT)

        time_quick_values = [0.1, 0.5, 1.0, 2.0, 5.0]
        for val in time_quick_values:
            btn = ttk.Button(time_quick_frame, text=f"{val}s", width=6,
                           command=lambda v=val: time_var.set(str(v)))
            btn.pack(side=tk.LEFT, padx=2)

        # 分隔线
        ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=10)

        # 偏移编辑
        offset_frame = ttk.Frame(dialog)
        offset_frame.pack(fill="x", padx=10, pady=5)

        # X偏移
        x_frame = ttk.Frame(offset_frame)
        x_frame.pack(fill="x", pady=2)
        ttk.Label(x_frame, text="X 偏移 (像素):").pack(side=tk.LEFT)
        offset_x_var = tk.StringVar(value=str(current_offset_x))
        offset_x_entry = ttk.Entry(x_frame, textvariable=offset_x_var, width=10)
        offset_x_entry.pack(side=tk.LEFT, padx=(5, 0))

        # Y偏移
        y_frame = ttk.Frame(offset_frame)
        y_frame.pack(fill="x", pady=2)
        ttk.Label(y_frame, text="Y 偏移 (像素):").pack(side=tk.LEFT)
        offset_y_var = tk.StringVar(value=str(current_offset_y))
        offset_y_entry = ttk.Entry(y_frame, textvariable=offset_y_var, width=10)
        offset_y_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 偏移快捷设置按钮
        offset_quick_frame = ttk.Frame(dialog)
        offset_quick_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(offset_quick_frame, text="偏移快捷设置:").pack(anchor="w")

        # 创建按钮网格
        button_grid_frame = ttk.Frame(offset_quick_frame)
        button_grid_frame.pack(fill="x", pady=2)

        offset_quick_values = [0, -20, -10, -5, 5, 10, 20]
        for i, val in enumerate(offset_quick_values):
            btn_text = "0" if val == 0 else f"{val:+d}"

            # X偏移按钮
            x_btn = ttk.Button(button_grid_frame, text=f"X{btn_text}", width=6,
                             command=lambda v=val: offset_x_var.set(str(v)))
            x_btn.grid(row=0, column=i, padx=1, pady=1)

            # Y偏移按钮
            y_btn = ttk.Button(button_grid_frame, text=f"Y{btn_text}", width=6,
                             command=lambda v=val: offset_y_var.set(str(v)))
            y_btn.grid(row=1, column=i, padx=1, pady=1)

        # 分隔线
        ttk.Separator(dialog, orient='horizontal').pack(fill="x", padx=10, pady=10)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=5)

        def save_changes():
            # 验证时间输入
            new_interval = self._validate_time_input(time_var.get(), "等待时间")
            if new_interval is None:
                return

            # 验证偏移输入
            new_offset_x = self._validate_offset_input(offset_x_var.get(), "X偏移")
            if new_offset_x is None:
                return

            new_offset_y = self._validate_offset_input(offset_y_var.get(), "Y偏移")
            if new_offset_y is None:
                return

            # 调用保存回调
            save_callback(new_interval, new_offset_x, new_offset_y)
            dialog.destroy()

        def cancel_changes():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel_changes).pack(side=tk.RIGHT)

        # 绑定快捷键
        dialog.bind('<Return>', lambda e: save_changes())
        dialog.bind('<Escape>', lambda e: cancel_changes())

        # 设置初始焦点
        time_entry.select_range(0, tk.END)
        time_entry.focus()

        return dialog

    def _display_recorded_actions(self):
        """显示完整的录制结果"""
        try:
            self.recording_text_area.config(state="normal")
            self.recording_text_area.delete(1.0, tk.END)

            if not self.recorded_actions:
                self.recording_text_area.insert(tk.END, "没有录制到任何动作\n")
            else:
                self.recording_text_area.insert(tk.END, f"录制完成，共 {len(self.recorded_actions)} 个动作：\n\n")

                for i, action in enumerate(self.recorded_actions, 1):
                    if action['type'] == 'click':
                        click_type = "双击" if action.get('double', False) else f"{action['button']}键点击"
                        action_text = f"{i}. 鼠标{click_type} 位置:({action['x']}, {action['y']})"
                        if action['template_image'] is not None:
                            action_text += " [模板已截取]"
                        action_text += f" 间隔:{action['interval']}s\n"
                    elif action['type'] == 'key':
                        action_text = f"{i}. 按键 '{action['key']}'"
                        if action['template_image'] is not None:
                            action_text += " [模板已截取]"
                        action_text += f" 间隔:{action['interval']}s\n"
                    else:
                        action_text = f"{i}. 未知动作类型: {action['type']}\n"

                    self.recording_text_area.insert(tk.END, action_text)

                self.recording_text_area.insert(tk.END, f"\n总录制时间: {self.last_action_time - self.recording_start_time:.2f} 秒\n")

            self.recording_text_area.config(state="disabled")

        except Exception as e:
            self.update_status(f"显示录制结果失败: {e}")


class RegionSelector:
    def __init__(self, parent_root, callback):
        self.parent_root = parent_root # Main app root, used for positioning if needed
        self.callback = callback
        self.start_x = None
        self.start_y = None
        self.current_x = None
        self.current_y = None
        self.rect = None

        # Create a top-level window for the overlay
        self.overlay = tk.Toplevel(self.parent_root)
        self.overlay.attributes("-fullscreen", True) # Make it fullscreen
        self.overlay.attributes("-alpha", 0.3)  # Make it semi-transparent (0.0 fully transparent, 1.0 opaque)
        self.overlay.attributes("-topmost", True) # Keep it on top
        self.overlay.overrideredirect(True) # Remove window decorations (title bar, borders)

        # A canvas on the overlay window to draw the selection rectangle
        self.canvas = tk.Canvas(self.overlay, cursor="crosshair", bg="grey") # Grey bg for visibility of transparency
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Add instructional text
        # Get screen width and height for text placement
        screen_width = self.overlay.winfo_screenwidth()
        screen_height = self.overlay.winfo_screenheight()

        instruction_text = "Click and drag to select a region. Press Esc to cancel."
        # Simple text shadow for better readability on varying backgrounds
        self.canvas.create_text(screen_width / 2 + 1, screen_height / 2 + 1, text=instruction_text, font=("Arial", 16), fill="black")
        self.canvas.create_text(screen_width / 2, screen_height / 2, text=instruction_text, font=("Arial", 16), fill="white")

        # Bind mouse events
        self.canvas.bind("<ButtonPress-1>", self.on_mouse_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_release)

        # Allow cancellation with Escape key
        self.overlay.bind("<Escape>", self.cancel_selection)

        # For some systems, focus might be needed
        self.overlay.focus_force()


    def on_mouse_press(self, event):
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)
        if self.rect:
            self.canvas.delete(self.rect)
        self.rect = None

    def on_mouse_drag(self, event):
        self.current_x = self.canvas.canvasx(event.x)
        self.current_y = self.canvas.canvasy(event.y)
        if self.rect:
            self.canvas.delete(self.rect)
        self.rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.current_x, self.current_y,
            outline='red', width=2
        )

    def on_mouse_release(self, event):
        if self.start_x is not None and self.start_y is not None and \
           self.current_x is not None and self.current_y is not None:
            # Get final coordinates relative to the screen
            # canvasx/canvasy give coordinates relative to canvas, which is fine as it's fullscreen
            x1 = min(self.start_x, self.current_x)
            y1 = min(self.start_y, self.current_y)
            x2 = max(self.start_x, self.current_x)
            y2 = max(self.start_y, self.current_y)

            # Adjust for the canvas being on the overlay window.
            # The coordinates from canvas should be screen coordinates as it's fullscreen.
            # However, ImageGrab.grab(bbox=...) expects screen coordinates.
            # We need to ensure these are truly screen coordinates.
            # For a simple fullscreen overlay, canvas coordinates should map directly.
            # If multi-monitor or specific window managers cause issues, this might need adjustment.
            # For now, assume direct mapping.

            bbox = (int(x1), int(y1), int(x2), int(y2))
            self.overlay.destroy() # Close the overlay window
            self.callback(bbox)
        else: # Click without drag, or some other issue
            self.overlay.destroy()
            self.callback(None)

    def cancel_selection(self, event=None):
        if self.overlay.winfo_exists():
            self.overlay.destroy()
        self.callback(None) # Signal cancellation


    def load_template_from_file(self):
        try:
            filepath = filedialog.askopenfilename(
                title="Select Template Image",
                filetypes=(("PNG files", "*.png"), ("JPEG files", "*.jpg;*.jpeg"), ("All files", "*.*"))
            )
            if not filepath:
                return

            self.update_status(f"Loading template from {filepath}...")
            self.template_image = cv2.imread(filepath)
            if self.template_image is None:
                raise ValueError("Could not read image file. Ensure it's a valid image format.")

            self.template_label.config(text=f"Template loaded ({self.template_image.shape[1]}x{self.template_image.shape[0]})")
            self.update_status("Template loaded successfully from file.")

            # Display thumbnail
            pil_image = Image.open(filepath)
            pil_image.thumbnail((200, 150))
            self.template_tk_image = ImageTk.PhotoImage(pil_image)
            self.template_display.config(image=self.template_tk_image)

        except Exception as e:
            messagebox.showerror("Load Error", f"Failed to load template: {e}")
            self.update_status(f"Error loading template: {e}")


    def toggle_automation(self):
        if self.is_automating:
            self.stop_automation()
        else:
            self.start_automation()

    def start_automation(self):
        if not self.template_image is not None: # Corrected condition: template must exist
            messagebox.showerror("Error", "Please capture or load a template image first.")
            return

        vid = self.vid_entry.get()
        vid_str = self.vid_entry.get()
        pid_str = self.pid_entry.get()
        try:
            interval = float(self.interval_entry.get())
            if interval <= 0:
                raise ValueError("Interval must be positive.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid interval: {e}")
            return

        if not vid_str or not pid_str:
            messagebox.showerror("Input Error", "VID and PID cannot be empty.")
            return

        try:
            # Convert hex string VID/PID from UI to integers
            vid_int = int(vid_str, 16)
            pid_int = int(pid_str, 16)
        except ValueError:
            messagebox.showerror("Input Error", "VID and PID must be valid hexadecimal numbers (e.g., 046D).")
            return

        try:
            self.update_status(f"Initializing mouse controller (VID:{vid_str}, PID:{pid_str})...")
            self.mouse_controller = ImplementationA(vid=vid_int, pid=pid_int) # Pass integer VID/PID
            self.update_status("ImplementationA object created. Initializing device...")
            self.wyUsbHandle = self.mouse_controller.wyhz_init() # Initialize and get handle
            if not self.wyUsbHandle: # wyhz_init might print error and exit, or return None/error
                # The original wyhz_init calls sys.exit on failure.
                # This might abruptly terminate the UI.
                # For robustness, wyhz_init should ideally raise an exception instead of sys.exit.
                # Assuming for now it might return a falsy value on failure if sys.exit is removed/handled.
                messagebox.showerror("Device Error", "Failed to get valid USB handle from wyhz_init.")
                self.update_status("Error: Failed to get valid USB handle.")
                self.mouse_controller = None
                return
            self.update_status("Mouse controller initialized and USB handle obtained.")
        except Exception as e:
            messagebox.showerror("Device Error", f"Failed to initialize mouse controller: {e}")
            self.update_status(f"Error initializing mouse controller: {e}")
            self.mouse_controller = None
            self.wyUsbHandle = None
            return

        self.is_automating = True
        self.start_stop_button.config(text="⏹️ 停止自动化", style="Danger.TButton")
        self.update_status("Automation started.")

        self.automation_thread = threading.Thread(target=self.automation_loop, args=(interval,), daemon=True)
        self.automation_thread.start()

    def stop_automation(self):
        self.is_automating = False
        if self.automation_thread and self.automation_thread.is_alive():
            # The thread will check self.is_automating and exit
             pass
        self.start_stop_button.config(text="🚀 开始自动化", style="Success.TButton")
        self.update_status("Automation stopped.")

        if self.mouse_controller and self.wyUsbHandle:
            try:
                # Use the close_device_handle method added to ImplementationA,
                # or directly UsbImplementationBase.wyhkm_close if preferred.
                self.mouse_controller.close_device_handle(self.wyUsbHandle)
                self.update_status("USB device handle released.")
            except Exception as e:
                self.update_status(f"Error releasing USB device handle: {e}")

        self.mouse_controller = None
        self.wyUsbHandle = None


    def automation_loop(self, interval):
        while self.is_automating:
            if self.template_image is None:
                self.update_status("Error: Template image is missing. Stopping automation.")
                self.is_automating = False # Stop loop if template disappears
                break
            try:
                self.update_status("Searching for template...")
                screenshot_pil = ImageGrab.grab()
                current_screen_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                # Image matching
                # Ensure template_image is not None before accessing shape
                if self.template_image is None: # Should be caught above, but defensive check
                    self.update_status("Critical Error: Template became None during loop. Stopping.")
                    self.is_automating = False
                    break

                result = cv2.matchTemplate(current_screen_cv, self.template_image, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                threshold = 0.8
                if max_val >= threshold:
                    template_h, template_w = self.template_image.shape[:2]
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    self.update_status(f"Template found at ({max_loc[0]}, {max_loc[1]}) with conf {max_val:.2f}. Clicking ({center_x}, {center_y}).")

                    if self.wyUsbHandle:
                        # Convert to int, as MoveTo might expect integers
                        click_x_int = int(round(center_x))
                        click_y_int = int(round(center_y))

                        self.wyUsbHandle.MoveTo(click_x_int, click_y_int)
                        # time.sleep(0.05) # Small delay often good for hardware interaction
                        self.wyUsbHandle.LeftClick()
                        self.update_status(f"Clicked at ({click_x_int}, {click_y_int}).")
                    else:
                        self.update_status("Error: USB Handle not available for click.")
                        print(f"DEBUG: wyUsbHandle not available. Would click at ({center_x}, {center_y})")
                else:
                    self.update_status(f"Template not found (max conf: {max_val:.2f} < {threshold}).")

            except Exception as e:
                # Log error to status bar, but don't stop the loop unless self.is_automating is false
                print(f"Error in automation loop: {e}") # Print to console for debugging
                self.update_status(f"Error: {e}")

            # Wait for the interval, but check is_automating frequently to allow quick stopping
            for _ in range(int(interval * 10)): # Check every 0.1s
                if not self.is_automating:
                    break
                time.sleep(0.1)

        self.update_status("Script cleared.")



if __name__ == "__main__":
    try:
        if platform.system() == "Windows":
            # Try to set Per Monitor V2 DPI awareness
            # Process per monitor DPI aware
            # (2) PROCESS_PER_MONITOR_DPI_AWARE = Per monitor V2
            # (1) PROCESS_SYSTEM_DPI_AWARE = System DPI aware
            # (0) PROCESS_DPI_UNAWARE = DPI Unaware
            ctypes.windll.shcore.SetProcessDpiAwareness(2)
            print("Attempted to set Per Monitor V2 DPI awareness.")
    except AttributeError:
        # If shcore.SetProcessDpiAwareness is not found (e.g., older Windows versions)
        # Try setting system DPI awareness as a fallback
        try:
            if platform.system() == "Windows":
                ctypes.windll.user32.SetProcessDPIAware()
                print("Attempted to set System DPI awareness (fallback).")
        except AttributeError:
            print("Could not set DPI awareness (SetProcessDPIAware not found).")
        except Exception as e:
            print(f"Error setting DPI awareness (fallback): {e}")
    except Exception as e:
        print(f"Error setting Per Monitor V2 DPI awareness: {e}")

    root = tk.Tk()
    app = AutomationApp(root)
    root.mainloop()
